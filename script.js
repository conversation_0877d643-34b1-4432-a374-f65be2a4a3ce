// Generate student data
function generateStudentData() {
    const firstNames = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ];

    const lastNames = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'Cook', 'Rogers',
        'Morgan', 'Peterson', 'Cooper', 'Reed', 'Bailey', 'Bell', 'Gomez', 'Kelly'
    ];

    const arabicNames = [
        'أحمد محمد', 'فاطمة علي', 'محمد أحمد', 'عائشة حسن', 'علي محمد', 'خديجة أحمد',
        'حسن علي', 'زينب محمد', 'عمر أحمد', 'مريم علي', 'يوسف محمد', 'سارة أحمد',
        'إبراهيم علي', 'نور محمد', 'عبدالله أحمد', 'ليلى علي', 'محمود محمد', 'رقية أحمد',
        'خالد علي', 'آمنة محمد', 'سعد أحمد', 'هدى علي', 'طارق محمد', 'سلمى أحمد',
        'ياسر علي', 'دعاء محمد', 'وليد أحمد', 'إيمان علي', 'سامي محمد', 'نادية أحمد',
        'رامي علي', 'سميرة محمد', 'فادي أحمد', 'لبنى علي', 'عماد محمد', 'رانيا أحمد'
    ];

    const grades = ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
    const gradesFr = ['9ème', '10ème', '11ème', '12ème'];
    const gradesAr = ['الصف التاسع', 'الصف العاشر', 'الصف الحادي عشر', 'الصف الثاني عشر'];
    const genders = ['Male', 'Female'];
    const statuses = ['Active', 'Suspended', 'Graduated', 'Transferred'];
    const statusWeights = [0.7, 0.1, 0.15, 0.05]; // 70% active, 10% suspended, 15% graduated, 5% transferred

    const birthPlaces = [
        'New York, NY', 'Los Angeles, CA', 'Chicago, IL', 'Houston, TX', 'Phoenix, AZ',
        'Philadelphia, PA', 'San Antonio, TX', 'San Diego, CA', 'Dallas, TX', 'San Jose, CA',
        'Austin, TX', 'Jacksonville, FL', 'Fort Worth, TX', 'Columbus, OH', 'Charlotte, NC',
        'San Francisco, CA', 'Indianapolis, IN', 'Seattle, WA', 'Denver, CO', 'Washington, DC',
        'Boston, MA', 'El Paso, TX', 'Nashville, TN', 'Detroit, MI', 'Oklahoma City, OK',
        'Portland, OR', 'Las Vegas, NV', 'Memphis, TN', 'Louisville, KY', 'Baltimore, MD',
        'Milwaukee, WI', 'Albuquerque, NM', 'Tucson, AZ', 'Fresno, CA', 'Sacramento, CA',
        'Kansas City, MO', 'Mesa, AZ', 'Atlanta, GA', 'Omaha, NE', 'Colorado Springs, CO',
        'Raleigh, NC', 'Miami, FL', 'Virginia Beach, VA', 'Oakland, CA', 'Minneapolis, MN',
        'Tulsa, OK', 'Arlington, TX', 'Tampa, FL', 'New Orleans, LA', 'Wichita, KS'
    ];

    const photos = [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=60&h=60&fit=crop&crop=face'
    ];

    function getRandomElement(arr, weights = null) {
        if (!weights) return arr[Math.floor(Math.random() * arr.length)];

        const random = Math.random();
        let sum = 0;
        for (let i = 0; i < weights.length; i++) {
            sum += weights[i];
            if (random <= sum) return arr[i];
        }
        return arr[arr.length - 1];
    }

    function generateBirthDate(gradeIndex) {
        const baseYear = 2024 - (9 + gradeIndex) - 6; // Approximate age calculation
        const year = baseYear + Math.floor(Math.random() * 2); // ±1 year variation
        const month = Math.floor(Math.random() * 12) + 1;
        const day = Math.floor(Math.random() * 28) + 1;

        const months = ['January', 'February', 'March', 'April', 'May', 'June',
                       'July', 'August', 'September', 'October', 'November', 'December'];

        return {
            formatted: `${months[month - 1]} ${day}, ${year}`,
            year: year,
            dateAdded: `2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`
        };
    }

    const students = [];

    for (let i = 1; i <= 200; i++) {
        const firstName = getRandomElement(firstNames);
        const lastName = getRandomElement(lastNames);
        const name = `${firstName} ${lastName}`;
        const nameAr = getRandomElement(arabicNames);
        const gradeIndex = Math.floor(Math.random() * grades.length);
        const gender = getRandomElement(genders);
        const status = getRandomElement(statuses, statusWeights);
        const birthData = generateBirthDate(gradeIndex);

        const baseFees = [2300, 2500, 2700, 2900][gradeIndex];
        const fees = baseFees + Math.floor(Math.random() * 200) - 100; // ±100 variation
        const amountPaid = Math.floor(Math.random() * (fees + 1));
        const remaining = fees - amountPaid;

        students.push({
            id: `ST2024${String(i).padStart(3, '0')}`,
            name: name,
            nameAr: nameAr,
            level: grades[gradeIndex],
            levelFr: gradesFr[gradeIndex],
            levelAr: gradesAr[gradeIndex],
            birth: birthData.formatted,
            birthYear: birthData.year,
            birthPlace: getRandomElement(birthPlaces),
            gender: gender,
            status: status,
            fees: fees,
            amountPaid: amountPaid,
            remaining: remaining,
            photo: getRandomElement(photos),
            date: `Jan ${Math.floor(Math.random() * 28) + 1}`,
            dateAdded: birthData.dateAdded
        });
    }

    return students;
}

// Alpine.js Data Store
function appData() {
    return {
        // Page navigation
        currentPage: 'students', // Default to students page

        // Student Data
        studentsData: generateStudentData(),

        // Mobile lazy loading properties
        mobileDisplayCount: 20, // Initial number of items to show on mobile
        mobileLoadIncrement: 10, // Number of items to load when scrolling
        isLoadingMore: false,

        // State variables
        currentPage: 1,
        perPage: 10,
        sortColumn: '',
        sortDirection: '',
        searchQuery: '',
        gradeFilter: '',
        genderFilter: '',
        birthYearFilter: '',
        dateFromFilter: '',
        dateToFilter: '',
        selectedStudentIds: new Set(),
        quickFilter: 'all', // Quick filter state

        // Computed properties
        get filteredData() {
            return this.studentsData.filter(student => {
                // Quick filter
                let matchesQuickFilter = true;
                if (this.quickFilter !== 'all') {
                    switch (this.quickFilter) {
                        case 'paid':
                            matchesQuickFilter = student.amountPaid > 0 && student.remaining > 0;
                            break;
                        case 'no-payments':
                            matchesQuickFilter = student.amountPaid === 0;
                            break;
                        case 'all-paid':
                            matchesQuickFilter = student.remaining === 0;
                            break;
                        case 'enrolled-today':
                            const today = new Date().toISOString().split('T')[0];
                            matchesQuickFilter = student.dateAdded === today;
                            break;
                    }
                }

                // Search filter
                const matchesSearch = !this.searchQuery ||
                    student.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                    student.level.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                    student.id.toLowerCase().includes(this.searchQuery.toLowerCase());

                // Grade filter
                const matchesGrade = !this.gradeFilter || student.level === this.gradeFilter;

                // Gender filter
                const matchesGender = !this.genderFilter || student.gender === this.genderFilter;

                // Birth year filter
                const matchesBirthYear = !this.birthYearFilter || student.birthYear.toString() === this.birthYearFilter;

                // Date range filter
                let matchesDateRange = true;
                if (this.dateFromFilter || this.dateToFilter) {
                    const studentDate = new Date(student.dateAdded);
                    if (this.dateFromFilter) {
                        const fromDate = new Date(this.dateFromFilter);
                        matchesDateRange = matchesDateRange && studentDate >= fromDate;
                    }
                    if (this.dateToFilter) {
                        const toDate = new Date(this.dateToFilter);
                        matchesDateRange = matchesDateRange && studentDate <= toDate;
                    }
                }

                return matchesQuickFilter && matchesSearch && matchesGrade && matchesGender && matchesBirthYear && matchesDateRange;
            }).sort((a, b) => {
                if (!this.sortColumn) return 0;

                let aVal = a[this.sortColumn];
                let bVal = b[this.sortColumn];

                if (this.sortColumn === 'birth') {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                } else if (this.sortColumn === 'level') {
                    aVal = parseInt(aVal.replace('Grade ', ''));
                    bVal = parseInt(bVal.replace('Grade ', ''));
                } else if (this.sortColumn === 'levelFr') {
                    aVal = parseInt(aVal.replace('ème', ''));
                    bVal = parseInt(bVal.replace('ème', ''));
                } else if (['fees', 'amountPaid', 'remaining'].includes(this.sortColumn)) {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        },

        get paginatedData() {
            const startIndex = (this.currentPage - 1) * this.perPage;
            const endIndex = startIndex + this.perPage;
            return this.filteredData.slice(startIndex, endIndex);
        },

        get totalPages() {
            return Math.ceil(this.filteredData.length / this.perPage);
        },

        get visiblePages() {
            const total = this.totalPages;
            const current = this.currentPage;

            if (total <= 7) {
                // If total pages is 7 or less, show all pages
                return Array.from({length: total}, (_, i) => i + 1);
            }

            const pages = [];

            // Always show first page
            pages.push(1);

            // Calculate the range around current page
            let start = Math.max(2, current - 2);
            let end = Math.min(total - 1, current + 2);

            // Adjust range to always show 5 pages in the middle when possible
            if (current <= 4) {
                // Near the beginning
                start = 2;
                end = Math.min(total - 1, 5);
            } else if (current >= total - 3) {
                // Near the end
                start = Math.max(2, total - 4);
                end = total - 1;
            }

            // Add ellipsis after first page if needed
            if (start > 2) {
                pages.push('...');
            }

            // Add the middle pages
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            // Add ellipsis before last page if needed
            if (end < total - 1) {
                pages.push('...');
            }

            // Always show last page (if more than 1 page total)
            if (total > 1) {
                pages.push(total);
            }

            return pages;
        },

        // Mobile lazy loading computed properties
        get mobileDisplayData() {
            return this.filteredData.slice(0, this.mobileDisplayCount);
        },

        get hasMoreMobileData() {
            return this.mobileDisplayCount < this.filteredData.length;
        },

        // Methods
        navigateToPage(page) {
            this.currentPage = page;
            console.log('Navigated to page:', page);
        },

        formatBirthDate(birthDate) {
            const date = new Date(birthDate);
            return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
        },

        sortData(column) {
            if (this.sortColumn === column) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortColumn = column;
                this.sortDirection = 'asc';
            }
        },

        getSortIcon(column) {
            if (this.sortColumn !== column) return 'unfold_more';
            return this.sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
        },

        getSortClass(column) {
            if (this.sortColumn !== column) return '';
            return this.sortDirection === 'asc' ? 'sort-asc' : 'sort-desc';
        },

        handleTableRowClick(event, student) {
            // Don't trigger if action buttons were clicked
            if (event.target.closest('.table-actions')) return;

            // Check if checkbox was clicked
            const checkbox = event.target.closest('.mdc-data-table__cell--checkbox');
            if (checkbox) {
                const checkboxInput = checkbox.querySelector('.row-checkbox');
                if (checkboxInput) {
                    checkboxInput.checked = !checkboxInput.checked;
                    // Update row selection state
                    updateRowSelectionState(checkboxInput);
                    updateSelectAllState();
                    updateSelectedRowsDisplay();
                }
                return;
            }

            // Extract student data for bottom sheet (fallback if no checkbox)
            const studentData = {
                id: student.id,
                name: student.name,
                level: student.level,
                details: `Born: ${student.birth} • Student ID: ${student.id}`,
                photo: `url('${student.photo}')`
            };

            showBottomSheet(studentData);
        },

        handleMoreAction(event, student) {
            // Check if we're on desktop (width > 768px)
            if (window.innerWidth > 768) {
                // Show dropdown menu on desktop
                console.log('Show dropdown menu for:', student.name);
                // TODO: Implement dropdown menu functionality
            } else {
                // Show bottom sheet on mobile
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            }
        },

        deleteStudent(student) {
            // Show confirmation dialog
            showConfirmDialog({
                title: 'Delete Student',
                message: `Are you sure you want to delete ${student.name}? This action cannot be undone.`,
                icon: 'delete',
                iconType: 'error',
                confirmText: 'Delete',
                cancelText: 'Cancel',
                confirmStyle: 'danger',
                onConfirm: () => {
                    // Remove student from data array
                    const index = this.studentsData.findIndex(s => s.id === student.id);
                    if (index !== -1) {
                        this.studentsData.splice(index, 1);
                        console.log('Student deleted:', student.name, 'ID:', student.id);

                        // Reset to first page if current page is empty
                        if (this.paginatedData.length === 0 && this.currentPage > 1) {
                            this.currentPage = this.currentPage - 1;
                        }
                    }
                },
                onCancel: () => {
                    console.log('Delete cancelled for:', student.name);
                }
            });
        },

        setQuickFilter(filterType) {
            // Update active chip styling
            document.querySelectorAll('.quick-filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

            // Set the filter
            this.quickFilter = filterType;
            this.currentPage = 1; // Reset to first page when filtering
        },

        clearAllFilters() {
            // Reset all filter states
            this.searchQuery = '';
            this.gradeFilter = '';
            this.genderFilter = '';
            this.birthYearFilter = '';
            this.dateFromFilter = '';
            this.dateToFilter = '';
            this.quickFilter = 'all';
            this.currentPage = 1;

            // Update quick filter chip styling
            document.querySelectorAll('.quick-filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector('[data-filter="all"]').classList.add('active');

            // Clear search input
            const searchInput = document.getElementById('table-search');
            if (searchInput) {
                searchInput.value = '';
            }

            // Clear mobile search input
            const mobileSearchInput = document.getElementById('mobile-search-input');
            if (mobileSearchInput) {
                mobileSearchInput.value = '';
            }

            // Reset filter selects and date pickers if they exist
            // This will be handled by the filter system
            console.log('All filters cleared');
        },

        toggleStudentSelection(studentId) {
            if (this.selectedStudentIds.has(studentId)) {
                this.selectedStudentIds.delete(studentId);
            } else {
                this.selectedStudentIds.add(studentId);
            }
            console.log('Selected students:', Array.from(this.selectedStudentIds));
        },

        isStudentSelected(studentId) {
            return this.selectedStudentIds.has(studentId);
        },

        selectAllStudents() {
            const allVisible = this.paginatedData.every(student => this.selectedStudentIds.has(student.id));
            if (allVisible) {
                // Deselect all visible students
                this.paginatedData.forEach(student => {
                    this.selectedStudentIds.delete(student.id);
                });
            } else {
                // Select all visible students
                this.paginatedData.forEach(student => {
                    this.selectedStudentIds.add(student.id);
                });
            }
        },

        get allVisibleSelected() {
            return this.paginatedData.length > 0 && this.paginatedData.every(student => this.selectedStudentIds.has(student.id));
        },

        get someVisibleSelected() {
            return this.paginatedData.some(student => this.selectedStudentIds.has(student.id));
        },

        // Pagination methods
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        changePerPage(newPerPage) {
            this.perPage = parseInt(newPerPage);
            this.currentPage = 1; // Reset to first page
        },

        // Mobile lazy loading methods
        loadMoreStudents() {
            if (this.isLoadingMore || !this.hasMoreMobileData) return;

            this.isLoadingMore = true;

            // Minimal loading delay for immediate response
            setTimeout(() => {
                this.mobileDisplayCount += this.mobileLoadIncrement;

                // Wait for Alpine.js to render the new items before hiding spinner
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.isLoadingMore = false;
                    }, 100); // Minimal delay for smooth transition
                });
            }, 150); // Reduced delay for faster response
        },

        resetMobileDisplay() {
            this.mobileDisplayCount = 20;
            this.isLoadingMore = false;
        },

        // Show bottom sheet with student data
        showBottomSheet(studentData) {
            // Call the global showBottomSheet function
            showBottomSheet(studentData);
        }
    }
}

// Watch for filter changes to reset mobile display
function watchFilterChanges() {
    const alpineElement = document.querySelector('[x-data="studentsApp()"]');
    if (!alpineElement) return;

    // Create a MutationObserver to watch for Alpine.js data changes
    let lastFilteredLength = 0;

    setInterval(() => {
        const alpineData = Alpine.$data(alpineElement);
        if (alpineData && alpineData.filteredData.length !== lastFilteredLength) {
            lastFilteredLength = alpineData.filteredData.length;
            alpineData.resetMobileDisplay();
        }
    }, 500);
}

// Initialize Material Design Components
mdc.autoInit();

// Initialize page preloader spinner
let pagePreloaderSpinner;
if (window.mdc && window.mdc.circularProgress) {
    const preloaderElement = document.getElementById('page-preloader-spinner');
    if (preloaderElement) {
        pagePreloaderSpinner = new mdc.circularProgress.MDCCircularProgress(preloaderElement);
    }
}

// Loading and Animation Functions
function showPagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.remove('hidden');

    // Start the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.open();
    }
}

function hidePagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.add('hidden');

    // Stop the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.close();
    }

    // Remove preloader from DOM after animation completes
    setTimeout(() => {
        preloader.style.display = 'none';
    }, 500);
}

function showLoadingOverlay(text = 'Processing...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = document.getElementById('loading-text');
    loadingText.textContent = text;
    overlay.classList.add('active');
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    overlay.classList.remove('active');
}

// Targeted Loading Functions
function showTargetedLoading(targetSelector, text = 'Loading...') {
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;

    // Find or create loading overlay for this target
    let overlay = targetElement.querySelector('.target-loading-overlay');
    if (!overlay) {
        // Create overlay if it doesn't exist
        overlay = document.createElement('div');
        overlay.className = 'target-loading-overlay';
        overlay.innerHTML = `
            <div class="target-loading-content">
                <div class="simple-spinner">
                    <svg class="spinner-svg" viewBox="0 0 50 50">
                        <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#6200ea" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
                <div class="target-loading-text"></div>
            </div>
        `;
        targetElement.appendChild(overlay);
    }

    // Update text and show overlay
    const loadingText = overlay.querySelector('.target-loading-text');
    if (loadingText) {
        loadingText.textContent = text;
    }

    // Ensure target element has relative positioning
    const computedStyle = window.getComputedStyle(targetElement);
    if (computedStyle.position === 'static') {
        targetElement.style.position = 'relative';
    }

    overlay.classList.add('active');
}

function hideTargetedLoading(targetSelector) {
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;

    const overlay = targetElement.querySelector('.target-loading-overlay');
    if (overlay) {
        overlay.classList.remove('active');
    }
}

// Simulate loading delay for better UX (can be removed in production)
function simulateLoading(callback, delay = 300) {
    setTimeout(callback, delay);
}

// Checkbox functionality
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (checkedBoxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedBoxes.length === rowCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

function updateSelectedRowsDisplay() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    console.log(`Selected ${checkedBoxes.length} rows`);
    // Here you can add UI updates for selected rows count
}

function getSelectedStudentIds() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkedBoxes).map(checkbox => {
        return checkbox.closest('.mdc-data-table__row').dataset.studentId;
    });
}

// Student Data
const studentsData = [
    {
        id: 'ST2024001',
        name: 'Alexander Thompson',
        nameAr: 'ألكسندر تومسون',
        level: 'Grade 10',
        levelFr: '10ème',
        levelAr: 'الصف العاشر',
        birth: 'March 15, 2008',
        birthYear: 2008,
        gender: 'Male',
        status: 'Active',
        fees: 2500,
        amountPaid: 1800,
        remaining: 700,
        photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 15',
        dateAdded: '2024-01-15'
    },
    {
        id: 'ST2024002',
        name: 'Sophia Rodriguez',
        nameAr: 'صوفيا رودريغيز',
        level: 'Grade 11',
        levelFr: '11ème',
        levelAr: 'الصف الحادي عشر',
        birth: 'July 22, 2007',
        birthYear: 2007,
        gender: 'Female',
        status: 'Active',
        fees: 2700,
        amountPaid: 2700,
        remaining: 0,
        photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14',
        dateAdded: '2024-01-14'
    },
    {
        id: 'ST2024003',
        name: 'Marcus Johnson',
        nameAr: 'ماركوس جونسون',
        level: 'Grade 9',
        levelFr: '9ème',
        levelAr: 'الصف التاسع',
        birth: 'November 8, 2009',
        birthYear: 2009,
        gender: 'Male',
        status: 'Active',
        fees: 2300,
        amountPaid: 1200,
        remaining: 1100,
        photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14',
        dateAdded: '2024-01-14'
    },
    {
        id: 'ST2024004',
        name: 'Emma Williams',
        nameAr: 'إيما ويليامز',
        level: 'Grade 12',
        levelFr: '12ème',
        levelAr: 'الصف الثاني عشر',
        birth: 'February 3, 2006',
        birthYear: 2006,
        gender: 'Female',
        status: 'Graduated',
        fees: 2900,
        amountPaid: 2900,
        remaining: 0,
        photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13',
        dateAdded: '2024-01-13'
    },
    {
        id: 'ST2024005',
        name: 'Daniel Chen',
        nameAr: 'دانيال تشين',
        level: 'Grade 10',
        levelFr: '10ème',
        levelAr: 'الصف العاشر',
        birth: 'September 12, 2008',
        birthYear: 2008,
        gender: 'Male',
        status: 'Active',
        fees: 2500,
        amountPaid: 2000,
        remaining: 500,
        photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13',
        dateAdded: '2024-01-13'
    },
    {
        id: 'ST2024006',
        name: 'Isabella Garcia',
        nameAr: 'إيزابيلا غارسيا',
        level: 'Grade 11',
        levelFr: '11ème',
        levelAr: 'الصف الحادي عشر',
        birth: 'May 18, 2007',
        birthYear: 2007,
        gender: 'Female',
        status: 'Active',
        fees: 2700,
        amountPaid: 1500,
        remaining: 1200,
        photo: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12',
        dateAdded: '2024-01-12'
    },
    {
        id: 'ST2024007',
        name: 'Ryan Martinez',
        nameAr: 'ريان مارتينيز',
        level: 'Grade 9',
        levelFr: '9ème',
        levelAr: 'الصف التاسع',
        birth: 'December 1, 2009',
        birthYear: 2009,
        gender: 'Male',
        status: 'Suspended',
        fees: 2300,
        amountPaid: 800,
        remaining: 1500,
        photo: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12',
        dateAdded: '2024-01-12'
    },
    {
        id: 'ST2024008',
        name: 'Olivia Davis',
        nameAr: 'أوليفيا ديفيس',
        level: 'Grade 12',
        levelFr: '12ème',
        levelAr: 'الصف الثاني عشر',
        birth: 'April 7, 2006',
        birthYear: 2006,
        gender: 'Female',
        status: 'Active',
        fees: 2900,
        amountPaid: 2400,
        remaining: 500,
        photo: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 11',
        dateAdded: '2024-01-11'
    }
];

// Data Table State
let currentPage = 1;
let perPage = 10;
let sortColumn = '';
let sortDirection = '';
let searchQuery = '';
let filteredData = [...studentsData];
let selectedStudentIds = new Set(); // Track selected student IDs

// Dark Mode State
let isDarkMode = localStorage.getItem('darkMode') === 'true';

// Filter State
let gradeFilter = '';
let genderFilter = '';
let birthYearFilter = '';
let dateFromFilter = '';
let dateToFilter = '';

// MDC Component instances
let searchTextField;
let perPageSelect;
let dataTable;
let gradeFilterSelect;

// Modal component instances
let modalOverlay;
let modal;
let modalForm;
let modalTitle;
let modalSubmitBtn;
let modalCancelBtn;

// Confirmation dialog component instances
let confirmDialogOverlay;
let confirmDialog;
let confirmDialogIcon;
let confirmDialogTitle;
let confirmDialogMessage;
let confirmDialogConfirmBtn;
let confirmDialogCancelBtn;

// Mobile search components
let mobileSearchOverlay;
let mobileSearchInput;
let mobileSearchResults;

// Bottom navigation components
let bottomNavItems;

// Filter offcanvas components
let filterGradeSelect;
let filterGenderSelect;
let filterBirthYearSelect;
let dateFromPicker;
let dateToPicker;

// Data Table Functions
function filterData() {
    // Show targeted loading for table
    showTargetedLoading('.data-table-container', 'Filtering...');

    simulateLoading(() => {
        // Update Alpine.js data if available
        const alpineEl = document.querySelector('[x-data]');
        if (alpineEl && alpineEl._x_dataStack) {
            const alpineData = alpineEl._x_dataStack[0];
            if (alpineData) {
                // Sync filter values with Alpine.js
                alpineData.searchQuery = searchQuery;
                alpineData.gradeFilter = gradeFilter;
                alpineData.genderFilter = genderFilter;
                alpineData.birthYearFilter = birthYearFilter;
                alpineData.dateFromFilter = dateFromFilter;
                alpineData.dateToFilter = dateToFilter;
                alpineData.currentPage = 1;
            }
        }

        // Legacy filtering for backward compatibility
        filteredData = studentsData.filter(student => {
            // Search filter
            const matchesSearch = !searchQuery ||
                student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                student.level.toLowerCase().includes(searchQuery.toLowerCase()) ||
                student.id.toLowerCase().includes(searchQuery.toLowerCase());

            // Grade filter
            const matchesGrade = !gradeFilter || student.level === gradeFilter;

            // Gender filter
            const matchesGender = !genderFilter || student.gender === genderFilter;

            // Birth year filter
            const matchesBirthYear = !birthYearFilter || student.birthYear.toString() === birthYearFilter;

            // Date range filter
            let matchesDateRange = true;
            if (dateFromFilter || dateToFilter) {
                const studentDate = new Date(student.dateAdded);
                if (dateFromFilter) {
                    const fromDate = new Date(dateFromFilter);
                    matchesDateRange = matchesDateRange && studentDate >= fromDate;
                }
                if (dateToFilter) {
                    const toDate = new Date(dateToFilter);
                    matchesDateRange = matchesDateRange && studentDate <= toDate;
                }
            }

            return matchesSearch && matchesGrade && matchesGender && matchesBirthYear && matchesDateRange;
        });

        currentPage = 1;
        updateHeaderFilterState();
        updateFilterChips();

        // Hide targeted loading
        hideTargetedLoading('.data-table-container');
    }, 300);
}

function sortData(column) {
    // Show targeted loading for table
    showTargetedLoading('.data-table-container', 'Sorting...');

    simulateLoading(() => {
        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }

        filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'birth') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (column === 'level') {
                // Sort grades numerically (Grade 9, Grade 10, etc.)
                aVal = parseInt(aVal.replace('Grade ', ''));
                bVal = parseInt(bVal.replace('Grade ', ''));
            } else if (column === 'levelFr') {
                // Sort French grades numerically (9ème, 10ème, etc.)
                aVal = parseInt(aVal.replace('ème', ''));
                bVal = parseInt(bVal.replace('ème', ''));
            } else if (column === 'fees' || column === 'amountPaid' || column === 'remaining') {
                // Sort numeric values
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            }

            if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        updateSortHeaders();

        // Hide targeted loading
        hideTargetedLoading('.data-table-container');
    }, 250);
}

function updateSortHeaders() {
    document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.textContent = 'unfold_more';
        }

        if (th.dataset.column === sortColumn) {
            th.classList.add(sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            if (icon) {
                icon.textContent = sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
            }
        }
    });
}



function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / perPage);
    const startItem = filteredData.length === 0 ? 0 : (currentPage - 1) * perPage + 1;
    const endItem = Math.min(currentPage * perPage, filteredData.length);

    // Update pagination info
    document.getElementById('pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${filteredData.length} students`;

    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;

    // Update page numbers
    const pagesContainer = document.getElementById('pagination-pages');
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    pagesContainer.innerHTML = '';
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `pagination-page ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.addEventListener('click', (e) => {
            if (i !== currentPage) {
                createRipple(e, pageBtn, true);
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage = i;
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });
        pagesContainer.appendChild(pageBtn);
    }
}

function attachTableEventListeners() {
    // Initialize MDC checkboxes
    document.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
        if (window.mdc && window.mdc.checkbox) {
            new mdc.checkbox.MDCCheckbox(checkbox);
        }
    });

    // Add click handlers to table rows (without ripple effect)
    document.querySelectorAll('.mdc-data-table__row.table-row').forEach(row => {
        row.addEventListener('click', (e) => {
            // Don't trigger if action button or checkbox area was clicked directly
            if (e.target.closest('.mdc-icon-button') || e.target.closest('.mdc-checkbox')) return;

            // Find the checkbox in this row
            const checkbox = row.querySelector('.row-checkbox');

            if (checkbox) {
                // Toggle checkbox state
                checkbox.checked = !checkbox.checked;

                // Trigger change event to ensure all handlers are called
                checkbox.dispatchEvent(new Event('change'));

                // Don't show bottom sheet when toggling checkbox
                return;
            }

            // Extract student data for bottom sheet (fallback if no checkbox)
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);

            if (student) {
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            }
        });
    });

    // Handle individual row checkbox changes
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            updateRowSelectionState(checkbox);
            updateSelectAllState();
            updateSelectedRowsDisplay();
        });
    });

    // Add ripple effects to action buttons
    document.querySelectorAll('.table-actions .mdc-icon-button').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            createRipple(e, btn, true);

            const row = btn.closest('.mdc-data-table__row');
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);
            const action = btn.title.toLowerCase();

            if (action.includes('more')) {
                // Check if we're on desktop (width > 768px)
                if (window.innerWidth > 768) {
                    // Show dropdown menu for desktop
                    showTableActionDropdown(btn, student);
                } else {
                    // Show bottom sheet for mobile
                    const studentData = {
                        id: student.id,
                        name: student.name,
                        level: student.level,
                        details: `Born: ${student.birth} • Student ID: ${student.id}`,
                        photo: `url('${student.photo}')`
                    };
                    showBottomSheet(studentData);
                }
            } else {
                console.log(`${action} student:`, student.name);
            }
        });
    });
}

// Header Filter Functions
function updateHeaderFilterState() {
    // Visual feedback can be added here if needed
    console.log('Filters applied:', { gradeFilter, genderFilter, birthYearFilter, dateFromFilter, dateToFilter });
}

// Filter Chips Functions
function updateFilterChips() {
    const container = document.getElementById('filter-chips-container');
    container.innerHTML = '';

    const activeFilters = [];

    if (gradeFilter) {
        activeFilters.push({ type: 'grade', label: `Grade: ${gradeFilter}`, value: gradeFilter });
    }
    if (genderFilter) {
        activeFilters.push({ type: 'gender', label: `Gender: ${genderFilter}`, value: genderFilter });
    }
    if (birthYearFilter) {
        activeFilters.push({ type: 'birthYear', label: `Birth Year: ${birthYearFilter}`, value: birthYearFilter });
    }
    if (dateFromFilter && dateToFilter) {
        activeFilters.push({ type: 'dateRange', label: `Date: ${dateFromFilter} to ${dateToFilter}`, value: `${dateFromFilter}|${dateToFilter}` });
    } else if (dateFromFilter) {
        activeFilters.push({ type: 'dateFrom', label: `From: ${dateFromFilter}`, value: dateFromFilter });
    } else if (dateToFilter) {
        activeFilters.push({ type: 'dateTo', label: `Until: ${dateToFilter}`, value: dateToFilter });
    }

    activeFilters.forEach(filter => {
        const chip = document.createElement('div');
        chip.className = 'filter-chip';
        chip.innerHTML = `
            <span>${filter.label}</span>
            <button class="filter-chip-remove" onclick="removeFilter('${filter.type}')">
                <span class="material-icons">close</span>
            </button>
        `;
        container.appendChild(chip);
    });
}

function removeFilter(filterType) {
    switch (filterType) {
        case 'grade':
            gradeFilter = '';
            if (filterGradeSelect) {
                filterGradeSelect.selectedIndex = 0;
            }
            break;
        case 'gender':
            genderFilter = '';
            if (filterGenderSelect) {
                filterGenderSelect.selectedIndex = 0;
            }
            break;
        case 'birthYear':
            birthYearFilter = '';
            if (filterBirthYearSelect) {
                filterBirthYearSelect.selectedIndex = 0;
            }
            break;
        case 'dateRange':
        case 'dateFrom':
        case 'dateTo':
            dateFromFilter = '';
            dateToFilter = '';
            if (dateFromPicker) {
                dateFromPicker.clear();
            }
            if (dateToPicker) {
                dateToPicker.clear();
            }
            break;
    }
    filterData();
}

// Row Selection Functions
function updateRowSelectionState(checkbox) {
    const row = checkbox.closest('.mdc-data-table__row');
    if (row) {
        const studentId = row.dataset.studentId;

        if (checkbox.checked) {
            row.classList.add('row-selected');
            selectedStudentIds.add(studentId);
        } else {
            row.classList.remove('row-selected');
            selectedStudentIds.delete(studentId);
        }
    }
}

function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (selectAllCheckbox) {
        if (checkedBoxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedBoxes.length === rowCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
}

function updateSelectedRowsDisplay() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    console.log(`${checkedBoxes.length} rows selected`);
    // You can add more UI updates here, like showing a selection toolbar
}

function restoreSelectionStates() {
    // Restore checkbox states and row highlighting after table re-render
    document.querySelectorAll('.mdc-data-table__row.table-row').forEach(row => {
        const studentId = row.dataset.studentId;
        const checkbox = row.querySelector('.row-checkbox');

        if (checkbox && selectedStudentIds.has(studentId)) {
            checkbox.checked = true;
            row.classList.add('row-selected');
        }
    });

    // Update select-all state
    updateSelectAllState();
}

// Table Action Dropdown Functions
function showTableActionDropdown(button, student) {
    // Close any existing dropdown
    closeTableActionDropdown();

    // Create dropdown menu
    const dropdown = document.createElement('div');
    dropdown.className = 'table-action-menu';
    dropdown.id = 'table-action-dropdown';

    dropdown.innerHTML = `
        <button class="table-action-menu-item" data-action="view">
            <span class="material-icons">person</span>
            <div class="table-action-menu-content">
                <div class="table-action-menu-title">View Details</div>
                <div class="table-action-menu-subtitle">See complete student information</div>
            </div>
        </button>
        <button class="table-action-menu-item" data-action="edit">
            <span class="material-icons">edit</span>
            <div class="table-action-menu-content">
                <div class="table-action-menu-title">Edit Student</div>
                <div class="table-action-menu-subtitle">Update student information</div>
            </div>
        </button>
        <button class="table-action-menu-item" data-action="payment">
            <span class="material-icons">payment</span>
            <div class="table-action-menu-content">
                <div class="table-action-menu-title">Add Payment Record</div>
                <div class="table-action-menu-subtitle">Record a new payment</div>
            </div>
        </button>
        <button class="table-action-menu-item delete" data-action="delete">
            <span class="material-icons">delete</span>
            <div class="table-action-menu-content">
                <div class="table-action-menu-title">Delete Student</div>
                <div class="table-action-menu-subtitle">Remove student from system</div>
            </div>
        </button>
    `;

    // Position dropdown using Material Design positioning logic
    document.body.appendChild(dropdown);

    // Calculate position relative to button with smart positioning
    const buttonRect = button.getBoundingClientRect();
    const dropdownWidth = 200;
    const dropdownHeight = 180; // Estimated height (4 items × ~45px each)
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const margin = 8; // Margin from viewport edges

    // Horizontal positioning
    let left = buttonRect.right - dropdownWidth; // Default: align right edge

    // If dropdown would go off the left edge, align to left edge of button
    if (left < margin) {
        left = buttonRect.left;
    }

    // If still off screen, align to viewport edge
    if (left < margin) {
        left = margin;
    } else if (left + dropdownWidth > viewportWidth - margin) {
        left = viewportWidth - dropdownWidth - margin;
    }

    // Vertical positioning
    let top = buttonRect.bottom + 4; // Default: below button
    let isAbove = false;

    // If dropdown would go off the bottom edge, show above button
    if (top + dropdownHeight > viewportHeight - margin) {
        top = buttonRect.top - dropdownHeight - 4;
        isAbove = true;
    }

    // If still off screen, align to viewport edge
    if (top < margin) {
        top = margin;
        isAbove = false;
    }

    // Add appropriate CSS class for animation direction
    if (isAbove) {
        dropdown.classList.add('dropdown-above');
    }

    dropdown.style.top = `${top}px`;
    dropdown.style.left = `${left}px`;

    // Show dropdown with animation
    setTimeout(() => {
        dropdown.classList.add('active');
    }, 10);

    // Add event listeners to menu items
    dropdown.querySelectorAll('.table-action-menu-item').forEach(item => {
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            const action = item.dataset.action;
            handleTableAction(action, student);
            closeTableActionDropdown();
        });
    });

    // Close dropdown when clicking outside
    setTimeout(() => {
        document.addEventListener('click', closeTableActionDropdown);
    }, 10);
}

function closeTableActionDropdown() {
    const dropdown = document.getElementById('table-action-dropdown');
    if (dropdown) {
        dropdown.classList.remove('active');
        setTimeout(() => {
            dropdown.remove();
        }, 200);
    }
    document.removeEventListener('click', closeTableActionDropdown);
}

function handleTableAction(action, student) {
    switch (action) {
        case 'view':
            console.log('View details for:', student.name);
            // You can implement view details functionality here
            break;
        case 'edit':
            console.log('Edit student:', student.name);
            // You can implement edit functionality here
            break;
        case 'payment':
            console.log('Add payment for:', student.name);
            // You can implement payment functionality here
            break;
        case 'delete':
            console.log('Delete student:', student.name);
            // You can implement delete functionality here
            break;
        default:
            console.log('Unknown action:', action);
    }
}

// Dark Mode Functions
function initializeDarkMode() {
    // Apply saved theme on page load
    if (isDarkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
        updateDarkModeIcon();
    }
}

function toggleDarkMode() {
    isDarkMode = !isDarkMode;

    if (isDarkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.removeAttribute('data-theme');
    }

    // Save preference to localStorage
    localStorage.setItem('darkMode', isDarkMode.toString());

    // Update icon
    updateDarkModeIcon();
}

function updateDarkModeIcon() {
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.textContent = isDarkMode ? 'light_mode' : 'dark_mode';
        darkModeToggle.title = isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode';
    }
}



function initializeDataTableControls() {
    // Initialize MDC Text Field for search
    const searchTextFieldEl = document.querySelector('.table-controls .mdc-text-field');
    if (searchTextFieldEl && window.mdc && window.mdc.textField) {
        searchTextField = new mdc.textField.MDCTextField(searchTextFieldEl);

        // Note: Search input is now handled by Alpine.js x-model="searchQuery"
        // No need for manual event listener
    }

    // Initialize MDC Select for per-page
    const perPageSelectEl = document.querySelector('.per-page-container .mdc-select');
    if (perPageSelectEl && window.mdc && window.mdc.select) {
        perPageSelect = new mdc.select.MDCSelect(perPageSelectEl);

        // Add change listener
        perPageSelect.listen('MDCSelect:change', () => {
            showTargetedLoading('.data-table-container', 'Loading...');

            simulateLoading(() => {
                // Update both global variable and Alpine.js data
                perPage = parseInt(perPageSelect.value);
                currentPage = 1;

                // Update Alpine.js data if available
                const alpineEl = document.querySelector('[x-data]');
                if (alpineEl && alpineEl._x_dataStack) {
                    const alpineData = alpineEl._x_dataStack[0];
                    if (alpineData) {
                        alpineData.perPage = perPage;
                        alpineData.currentPage = 1;
                    }
                }

                hideTargetedLoading('.data-table-container');
            }, 250);
        });
    }


}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Show page preloader initially
    showPagePreloader();

    // Simulate page loading time
    simulateLoading(() => {
        // Initialize MDC Data Table component
        const dataTableEl = document.querySelector('.mdc-data-table');
        if (dataTableEl && window.mdc && window.mdc.dataTable) {
            dataTable = new mdc.dataTable.MDCDataTable(dataTableEl);
        }

        // Initialize MDC Floating Action Button
        const fabEl = document.getElementById('add-student-fab');
        if (fabEl && window.mdc && window.mdc.ripple) {
            const fabRipple = new mdc.ripple.MDCRipple(fabEl);
        }

        // Initialize controls
        initializeDataTableControls();

        // Initialize select-all checkbox
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const rowCheckboxes = document.querySelectorAll('.row-checkbox');
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                    updateRowSelectionState(checkbox);
                });
                updateSelectedRowsDisplay();
            });
        }

        // Sorting is now handled by Alpine.js - no need for manual event listeners

        // Pagination is now handled by Alpine.js - no need for manual event listeners

        // Add event listeners for content header icons
        document.querySelector('.content-header .actions .material-icons[title="Export to Excel"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Export to Excel clicked');
            // TODO: Implement Excel export functionality
        });

        document.querySelector('.content-header .actions .material-icons[title="Import Data"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Import Data clicked');
            // TODO: Implement data import functionality
        });

        document.querySelector('.content-header .actions .material-icons[title="Toggle View"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Toggle View clicked');
            // TODO: Implement view toggle functionality (list/grid)
        });

        // Add event listener for floating action button (mobile - use regular modal)
        document.getElementById('add-student-fab').addEventListener('click', () => {
            openAddStudentModal();
        });

        // Add event listener for desktop add student button (use large modal on desktop)
        const addStudentBtn = document.getElementById('add-student-btn');
        if (addStudentBtn) {
            // Initialize MDC Button
            if (window.mdc && window.mdc.ripple) {
                new mdc.ripple.MDCRipple(addStudentBtn);
            }

            addStudentBtn.addEventListener('click', () => {
                // Use large modal on desktop (screen width > 768px)
                if (window.innerWidth > 768) {
                    openLargeStudentModal();
                } else {
                    openAddStudentModal();
                }
            });
        }

        // Initialize modal
        initializeModal();

        // Initialize confirmation dialog
        initializeConfirmDialog();

        // Initialize mobile search
        initializeMobileSearch();

        // Initialize mobile lazy loading
        initializeMobileLazyLoading();

        // Initialize filter change watcher
        watchFilterChanges();

        // Initialize scroll behavior (with delay to ensure DOM is ready)
        setTimeout(() => {
            initializeScrollBehavior();
        }, 500);

        // Initialize loading spinner
        initializeLoadingSpinner();

        // Initialize bottom navigation
        initializeBottomNavigation();

        // Initialize dark mode
        initializeDarkMode();

        // Add dark mode toggle event listener
        const darkModeToggle = document.getElementById('dark-mode-toggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', (e) => {
                createRipple(e, darkModeToggle, true);
                toggleDarkMode();
            });
        }

        // Alpine.js will handle rendering automatically

        // Hide page preloader after everything is loaded
        hidePagePreloader();
    }, 800); // Slightly longer delay to show the preloader
});

// Modal Functions
function initializeModal() {
    modalOverlay = document.getElementById('modal-overlay');
    modal = document.getElementById('modal');
    modalForm = document.getElementById('modal-form');
    modalTitle = document.getElementById('modal-title');
    modalSubmitBtn = document.getElementById('modal-submit');
    modalCancelBtn = document.getElementById('modal-cancel');

    // Initialize MDC buttons
    if (window.mdc && window.mdc.ripple) {
        new mdc.ripple.MDCRipple(modalSubmitBtn);
        new mdc.ripple.MDCRipple(modalCancelBtn);
    }

    // Add event listeners
    document.getElementById('modal-close').addEventListener('click', closeModal);
    modalCancelBtn.addEventListener('click', closeModal);
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeModal();
        }
    });

    // Handle form submission
    modalForm.addEventListener('submit', handleModalSubmit);

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modalOverlay.classList.contains('active')) {
            closeModal();
        }
    });
}

function openModal(config) {
    const {
        title = 'Modal',
        fields = [],
        sections = null,
        submitText = 'Save',
        onSubmit = null,
        data = {},
        large = false
    } = config;

    // Set modal title
    modalTitle.textContent = title;
    modalSubmitBtn.querySelector('.mdc-button__label').textContent = submitText;

    // Add or remove large modal class
    if (large) {
        modal.classList.add('modal--large');
    } else {
        modal.classList.remove('modal--large');
    }

    // Clear and populate form
    modalForm.innerHTML = '';

    if (sections && large) {
        // Create sectioned form for large modal
        sections.forEach(section => {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'form-section';

            if (section.title) {
                const titleDiv = document.createElement('div');
                titleDiv.className = 'form-section-title';
                titleDiv.textContent = section.title;
                sectionDiv.appendChild(titleDiv);
            }

            section.fields.forEach(field => {
                const fieldElement = createFormField(field, data[field.name] || '');
                sectionDiv.appendChild(fieldElement);
            });

            modalForm.appendChild(sectionDiv);
        });
    } else {
        // Create regular form
        fields.forEach(field => {
            const fieldElement = createFormField(field, data[field.name] || '');
            modalForm.appendChild(fieldElement);
        });
    }

    // Initialize MDC components for new fields
    initializeModalFormComponents();

    // Store submit handler
    modalForm.onSubmitHandler = onSubmit;

    // Show modal
    modalOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus first input
    setTimeout(() => {
        const firstInput = modalForm.querySelector('input, select, textarea');
        if (firstInput) {
            firstInput.focus();
        }
    }, 300);
}

function closeModal() {
    modalOverlay.classList.remove('active');
    document.body.style.overflow = '';

    // Clear form after animation
    setTimeout(() => {
        modalForm.innerHTML = '';
        modalForm.onSubmitHandler = null;
    }, 300);
}

function createFormField(field, value = '') {
    const {
        name,
        label,
        type = 'text',
        required = false,
        options = [],
        placeholder = '',
        rows = 3,
        width = 'normal' // 'normal', 'full', 'two-columns'
    } = field;

    const fieldDiv = document.createElement('div');
    let fieldClass = 'form-field';

    if (width === 'full') {
        fieldClass += ' form-field--full-width';
    } else if (width === 'two-columns') {
        fieldClass += ' form-field--two-columns';
    }

    fieldDiv.className = fieldClass;

    if (type === 'select') {
        fieldDiv.innerHTML = `
            <div class="mdc-select mdc-select--outlined mdc-small-input">
                <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                    <span class="mdc-select__selected-text-container">
                        <span class="mdc-select__selected-text"></span>
                    </span>
                    <span class="mdc-select__dropdown-icon">
                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                        </svg>
                    </span>
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label class="mdc-floating-label">${label}${required ? ' *' : ''}</label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
                <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                    <ul class="mdc-deprecated-list" role="listbox">
                        ${options.map(option => `
                            <li class="mdc-deprecated-list-item ${option.value === value ? 'mdc-deprecated-list-item--selected' : ''}"
                                data-value="${option.value}" role="option" ${option.value === value ? 'aria-selected="true"' : ''}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">${option.label}</span>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
        `;
    } else if (type === 'textarea') {
        fieldDiv.innerHTML = `
            <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea mdc-small-input">
                <span class="mdc-notched-outline">
                    <span class="mdc-notched-outline__leading"></span>
                    <span class="mdc-notched-outline__notch">
                        <span class="mdc-floating-label">${label}${required ? ' *' : ''}</span>
                    </span>
                    <span class="mdc-notched-outline__trailing"></span>
                </span>
                <span class="mdc-text-field__resizer">
                    <textarea class="mdc-text-field__input" name="${name}" rows="${rows}"
                              ${required ? 'required' : ''} placeholder="${placeholder}">${value}</textarea>
                </span>
            </div>
        `;
    } else {
        fieldDiv.innerHTML = `
            <div class="mdc-text-field mdc-text-field--outlined mdc-small-input">
                <input type="${type}" class="mdc-text-field__input" name="${name}"
                       value="${value}" ${required ? 'required' : ''} placeholder="${placeholder}">
                <div class="mdc-notched-outline">
                    <div class="mdc-notched-outline__leading"></div>
                    <div class="mdc-notched-outline__notch">
                        <label class="mdc-floating-label">${label}${required ? ' *' : ''}</label>
                    </div>
                    <div class="mdc-notched-outline__trailing"></div>
                </div>
            </div>
        `;
    }

    return fieldDiv;
}

function initializeModalFormComponents() {
    // Initialize MDC text fields
    modalForm.querySelectorAll('.mdc-text-field').forEach(textField => {
        if (window.mdc && window.mdc.textField) {
            new mdc.textField.MDCTextField(textField);
        }
    });

    // Initialize MDC select fields
    modalForm.querySelectorAll('.mdc-select').forEach(select => {
        if (window.mdc && window.mdc.select) {
            new mdc.select.MDCSelect(select);
        }
    });
}

function handleModalSubmit(e) {
    e.preventDefault();

    if (modalForm.onSubmitHandler) {
        const formData = new FormData(modalForm);
        const data = Object.fromEntries(formData.entries());
        modalForm.onSubmitHandler(data);
    }

    closeModal();
}

// Confirmation Dialog Functions
function initializeConfirmDialog() {
    confirmDialogOverlay = document.getElementById('confirm-dialog-overlay');
    confirmDialog = document.getElementById('confirm-dialog');
    confirmDialogIcon = document.getElementById('confirm-dialog-icon');
    confirmDialogTitle = document.getElementById('confirm-dialog-title');
    confirmDialogMessage = document.getElementById('confirm-dialog-message');
    confirmDialogConfirmBtn = document.getElementById('confirm-dialog-confirm');
    confirmDialogCancelBtn = document.getElementById('confirm-dialog-cancel');

    // Initialize MDC buttons
    if (window.mdc && window.mdc.ripple) {
        new mdc.ripple.MDCRipple(confirmDialogConfirmBtn);
        new mdc.ripple.MDCRipple(confirmDialogCancelBtn);
    }

    // Add event listeners
    confirmDialogCancelBtn.addEventListener('click', closeConfirmDialog);
    confirmDialogOverlay.addEventListener('click', (e) => {
        if (e.target === confirmDialogOverlay) {
            closeConfirmDialog();
        }
    });

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && confirmDialogOverlay.classList.contains('active')) {
            closeConfirmDialog();
        }
    });
}

function showConfirmDialog(config) {
    const {
        title = 'Confirm Action',
        message = 'Are you sure you want to proceed?',
        icon = 'warning',
        iconType = 'warning', // 'warning', 'error', 'info', 'success'
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        confirmStyle = 'primary', // 'primary', 'danger'
        onConfirm = () => {},
        onCancel = () => {}
    } = config;

    // Set dialog content
    confirmDialogTitle.textContent = title;
    confirmDialogMessage.textContent = message;
    confirmDialogConfirmBtn.querySelector('.mdc-button__label').textContent = confirmText;
    confirmDialogCancelBtn.querySelector('.mdc-button__label').textContent = cancelText;

    // Set icon
    confirmDialogIcon.querySelector('.material-icons').textContent = icon;

    // Reset icon classes
    confirmDialogIcon.className = 'confirm-dialog-icon';
    if (iconType !== 'warning') {
        confirmDialogIcon.classList.add(iconType);
    }

    // Set confirm button style
    confirmDialogConfirmBtn.className = 'mdc-button mdc-button--raised confirm-dialog-confirm';
    if (confirmStyle === 'danger') {
        confirmDialogConfirmBtn.classList.add('confirm-dialog-confirm--danger');
    }

    // Store handlers
    confirmDialogConfirmBtn.onClickHandler = onConfirm;
    confirmDialogCancelBtn.onClickHandler = onCancel;

    // Add confirm button click handler
    confirmDialogConfirmBtn.onclick = () => {
        if (confirmDialogConfirmBtn.onClickHandler) {
            confirmDialogConfirmBtn.onClickHandler();
        }
        closeConfirmDialog();
    };

    // Add cancel button click handler
    confirmDialogCancelBtn.onclick = () => {
        if (confirmDialogCancelBtn.onClickHandler) {
            confirmDialogCancelBtn.onClickHandler();
        }
        closeConfirmDialog();
    };

    // Show dialog
    confirmDialogOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus confirm button
    setTimeout(() => {
        confirmDialogConfirmBtn.focus();
    }, 300);
}

function closeConfirmDialog() {
    confirmDialogOverlay.classList.remove('active');
    document.body.style.overflow = '';

    // Clear handlers after animation
    setTimeout(() => {
        confirmDialogConfirmBtn.onClickHandler = null;
        confirmDialogCancelBtn.onClickHandler = null;
        confirmDialogConfirmBtn.onclick = null;
        confirmDialogCancelBtn.onclick = null;
    }, 300);
}

// Mobile Search Functions
function initializeMobileSearch() {
    mobileSearchOverlay = document.getElementById('mobile-search-overlay');
    mobileSearchInput = document.getElementById('mobile-search-input');
    mobileSearchResults = document.getElementById('mobile-search-results');

    // Add event listeners
    document.getElementById('mobile-search-btn').addEventListener('click', openMobileSearch);
    document.getElementById('mobile-search-back').addEventListener('click', closeMobileSearch);

    // Handle search input
    mobileSearchInput.addEventListener('input', handleMobileSearch);

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && mobileSearchOverlay.classList.contains('active')) {
            closeMobileSearch();
        }
    });

    // Close search when clicking outside results
    mobileSearchOverlay.addEventListener('click', (e) => {
        if (e.target === mobileSearchOverlay) {
            closeMobileSearch();
        }
    });
}

function openMobileSearch() {
    mobileSearchOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus input after animation
    setTimeout(() => {
        mobileSearchInput.focus();
    }, 300);

    // Clear previous search
    mobileSearchInput.value = '';
    mobileSearchResults.innerHTML = '';
}

function closeMobileSearch() {
    mobileSearchOverlay.classList.remove('active');
    document.body.style.overflow = '';
    mobileSearchInput.value = '';
    mobileSearchResults.innerHTML = '';
}

function handleMobileSearch() {
    const query = mobileSearchInput.value.trim().toLowerCase();

    if (query === '') {
        mobileSearchResults.innerHTML = '';
        return;
    }

    // Filter students based on search query
    const searchResults = studentsData.filter(student => {
        return student.name.toLowerCase().includes(query) ||
               student.level.toLowerCase().includes(query) ||
               student.id.toLowerCase().includes(query) ||
               (student.email && student.email.toLowerCase().includes(query));
    });

    renderMobileSearchResults(searchResults, query);
}

function renderMobileSearchResults(results, query) {
    if (results.length === 0) {
        mobileSearchResults.innerHTML = `
            <div class="mobile-search-no-results">
                <span class="material-icons">search_off</span>
                <div>No students found for "${query}"</div>
            </div>
        `;
        return;
    }

    mobileSearchResults.innerHTML = results.map(student => `
        <div class="mobile-search-result-item" data-student-id="${student.id}">
            <div class="mobile-search-result-photo" style="background-image: url('${student.photo}')"></div>
            <div class="mobile-search-result-info">
                <div class="mobile-search-result-name">${highlightSearchTerm(student.name, query)}</div>
                <div class="mobile-search-result-details">
                    ${student.level} • ${student.id}
                </div>
            </div>
        </div>
    `).join('');

    // Add click handlers to search results
    mobileSearchResults.querySelectorAll('.mobile-search-result-item').forEach(item => {
        item.addEventListener('click', (e) => {
            createRipple(e, item, true);

            const studentId = item.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);

            if (student) {
                // Close search and show student details
                closeMobileSearch();

                // Show student in bottom sheet (reuse existing functionality)
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    birth: student.birth,
                    photo: student.photo
                };

                showBottomSheet(studentData);
            }
        });
    });
}

function highlightSearchTerm(text, term) {
    if (!term) return text;

    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark style="background-color: var(--primary-alpha-16); color: var(--primary-color); padding: 0;">$1</mark>');
}

// Mobile Lazy Loading Functions
function initializeMobileLazyLoading() {
    // Only initialize on mobile devices
    if (window.innerWidth > 768) return;

    const studentsListContainer = document.querySelector('.students-list');
    if (!studentsListContainer) return;

    let observer;
    let isObserverActive = false;

    function createIntersectionObserver() {
        // Create intersection observer to watch for 3rd-to-last student
        observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Get Alpine.js instance
                    const alpineData = Alpine.$data(document.querySelector('[x-data="studentsApp()"]'));
                    if (alpineData && alpineData.hasMoreMobileData && !alpineData.isLoadingMore) {
                        alpineData.loadMoreStudents();
                    }
                }
            });
        }, {
            root: null, // Use viewport as root
            rootMargin: '50px', // Trigger 50px before element comes into view
            threshold: 0.1 // Trigger when 10% of element is visible
        });

        isObserverActive = true;
    }

    function updateObservedElement() {
        if (!observer || !isObserverActive) return;

        // Disconnect previous observations
        observer.disconnect();

        // Find all student items
        const studentItems = document.querySelectorAll('.student-item');

        if (studentItems.length >= 3) {
            // Observe the 3rd-to-last student item
            const thirdToLastIndex = studentItems.length - 3;
            const thirdToLastStudent = studentItems[thirdToLastIndex];

            if (thirdToLastStudent) {
                observer.observe(thirdToLastStudent);
            }
        }
    }

    // Create the observer
    createIntersectionObserver();

    // Initial setup of observed element
    setTimeout(() => {
        updateObservedElement();
    }, 500);

    // Watch for changes in the student list and update observed element
    const listObserver = new MutationObserver(() => {
        // Delay to allow Alpine.js to render new items
        setTimeout(() => {
            updateObservedElement();
        }, 100);
    });

    // Start observing the students list for changes
    listObserver.observe(studentsListContainer, {
        childList: true,
        subtree: true
    });

    // Handle window resize to disable on desktop
    function handleResize() {
        if (window.innerWidth > 768) {
            if (observer) {
                observer.disconnect();
                isObserverActive = false;
            }
            if (listObserver) {
                listObserver.disconnect();
            }
        } else if (!isObserverActive) {
            createIntersectionObserver();
            updateObservedElement();
        }
    }

    window.addEventListener('resize', handleResize);

    // Store cleanup function for potential future use
    window.cleanupMobileLazyLoading = () => {
        if (observer) observer.disconnect();
        if (listObserver) listObserver.disconnect();
        window.removeEventListener('resize', handleResize);
    };
}

// Loading Spinner Functions
function initializeLoadingSpinner() {
    // Initialize the loading spinner for mobile lazy loading
    setTimeout(() => {
        const loadingSpinner = document.querySelector('.loading-more-spinner .mdc-circular-progress');
        if (loadingSpinner && window.mdc && window.mdc.circularProgress) {
            const spinner = new mdc.circularProgress.MDCCircularProgress(loadingSpinner);
            // Store reference for potential future use
            window.mobileLoadingSpinner = spinner;
        }
    }, 1000);
}

// Scroll Behavior Functions
function initializeScrollBehavior() {
    let lastScrollTop = 0;
    let ticking = false;

    // Wait for elements to be available
    const fab = document.getElementById('add-student-fab');
    const fabLabel = fab ? fab.querySelector('.mdc-fab__label') : null;
    const bottomAppBar = document.getElementById('bottom-app-bar');

    console.log('Initializing scroll behavior...', {
        fab: !!fab,
        fabLabel: !!fabLabel,
        bottomAppBar: !!bottomAppBar,
        windowWidth: window.innerWidth,
        isMobile: window.innerWidth <= 768
    });

    if (!fab && !bottomAppBar) {
        console.log('No FAB or bottom app bar found, skipping scroll behavior');
        return;
    }

    // Force initial state
    if (fabLabel) {
        fabLabel.style.display = '';
        fabLabel.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        fabLabel.style.opacity = '1';
        fabLabel.style.transform = 'scale(1)';
    }

    if (bottomAppBar) {
        bottomAppBar.style.transition = 'transform 0.3s ease';
        bottomAppBar.style.transform = 'translateY(0)';
    }

    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                const scrollDelta = Math.abs(currentScrollTop - lastScrollTop);

                // Only react to significant scroll movements (more than 5px)
                if (scrollDelta > 5) {
                    console.log('Scroll detected:', {
                        currentScrollTop,
                        lastScrollTop,
                        scrollDelta,
                        windowWidth: window.innerWidth,
                        isMobile: window.innerWidth <= 768
                    });

                    if (currentScrollTop > lastScrollTop && currentScrollTop > 50) {
                        // Scrolling down - hide label and bottom bar
                        console.log('Scrolling down - hiding elements');
                        hideScrollElements();
                    } else if (currentScrollTop < lastScrollTop) {
                        // Scrolling up - show label and bottom bar
                        console.log('Scrolling up - showing elements');
                        showScrollElements();
                    }

                    lastScrollTop = Math.max(0, currentScrollTop); // Prevent negative values
                }

                ticking = false;
            });

            ticking = true;
        }
    }

    function hideScrollElements() {
        console.log('Hiding scroll elements', {
            fabLabel: !!fabLabel,
            bottomAppBar: !!bottomAppBar,
            isMobile: window.innerWidth <= 768
        });

        // Hide FAB label (on all devices) - use display none for complete removal
        if (fabLabel) {
            console.log('Hiding FAB label with display none');
            // First animate out with opacity and scale
            fabLabel.style.transition = 'opacity 0.15s ease, transform 0.15s ease';
            fabLabel.style.opacity = '0';
            fabLabel.style.transform = 'scale(0.8)';

            // Add class to fix FAB size when label is hidden
            const fab = document.getElementById('add-student-fab');
            if (fab) {
                fab.classList.add('label-hidden');
            }

            // After animation completes, set display to none
            setTimeout(() => {
                if (fabLabel.style.opacity === '0') {
                    fabLabel.style.display = 'none';
                }
            }, 150);
        }

        // Hide bottom app bar (only on mobile)
        if (bottomAppBar && window.innerWidth <= 768) {
            console.log('Hiding bottom app bar');
            bottomAppBar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            bottomAppBar.style.transform = 'translateY(100%)';
        }

        // Hide top app bar (on all devices)
        const topAppBar = document.querySelector('.app-bar');
        const contentHeader = document.querySelector('.content-header');
        if (topAppBar) {
            console.log('Hiding top app bar');
            topAppBar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            topAppBar.style.transform = 'translateY(-100%)';

            // Adjust content header position when app bar is hidden
            if (contentHeader) {
                contentHeader.style.transition = 'top 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                contentHeader.style.top = '0';
            }
        }
    }

    function showScrollElements() {
        console.log('Showing scroll elements', {
            fabLabel: !!fabLabel,
            bottomAppBar: !!bottomAppBar,
            isMobile: window.innerWidth <= 768
        });

        // Show FAB label (on all devices) - restore display first, then animate in
        if (fabLabel) {
            console.log('Showing FAB label with display restoration');

            // Remove class to restore FAB size
            const fab = document.getElementById('add-student-fab');
            if (fab) {
                fab.classList.remove('label-hidden');
            }

            // First restore display and set initial hidden state
            fabLabel.style.display = '';
            fabLabel.style.opacity = '0';
            fabLabel.style.transform = 'scale(0.8)';
            fabLabel.style.transition = 'opacity 0.2s ease, transform 0.2s ease';

            // Force reflow to ensure display change takes effect
            fabLabel.offsetHeight;

            // Then animate in
            requestAnimationFrame(() => {
                fabLabel.style.opacity = '1';
                fabLabel.style.transform = 'scale(1)';
            });
        }

        // Show bottom app bar (only on mobile)
        if (bottomAppBar && window.innerWidth <= 768) {
            console.log('Showing bottom app bar');
            bottomAppBar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            bottomAppBar.style.transform = 'translateY(0)';
        }

        // Show top app bar (on all devices)
        const topAppBar = document.querySelector('.app-bar');
        const contentHeader = document.querySelector('.content-header');
        if (topAppBar) {
            console.log('Showing top app bar');
            topAppBar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            topAppBar.style.transform = 'translateY(0)';

            // Restore content header position when app bar is shown
            if (contentHeader) {
                contentHeader.style.transition = 'top 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                contentHeader.style.top = '56px';
            }
        }
    }

    // Add multiple scroll event listeners for better mobile support
    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('scroll', handleScroll, { passive: true });
    document.body.addEventListener('scroll', handleScroll, { passive: true });

    // Also listen for touch events on mobile
    let touchStartY = 0;
    let touchEndY = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    document.addEventListener('touchend', (e) => {
        touchEndY = e.changedTouches[0].screenY;
        const touchDelta = Math.abs(touchEndY - touchStartY);

        if (touchDelta > 50) { // Significant touch movement
            if (touchEndY < touchStartY) {
                // Swiping up (scrolling down)
                console.log('Touch: Scrolling down');
                hideScrollElements();
            } else {
                // Swiping down (scrolling up)
                console.log('Touch: Scrolling up');
                showScrollElements();
            }
        }
    }, { passive: true });

    // Handle window resize
    window.addEventListener('resize', () => {
        console.log('Window resized:', window.innerWidth);

        if (window.innerWidth > 768) {
            // On desktop, always show FAB label with proper display
            if (fabLabel) {
                const fab = document.getElementById('add-student-fab');
                if (fab) {
                    fab.classList.remove('label-hidden');
                }
                fabLabel.style.display = '';
                fabLabel.style.opacity = '1';
                fabLabel.style.transform = 'scale(1)';
                fabLabel.style.pointerEvents = '';
            }
            // Always show bottom app bar on desktop
            if (bottomAppBar) {
                bottomAppBar.style.transform = 'translateY(0)';
            }
            // Always show top app bar on desktop
            const topAppBar = document.querySelector('.app-bar');
            const contentHeader = document.querySelector('.content-header');
            if (topAppBar) {
                topAppBar.style.transform = 'translateY(0)';
                if (contentHeader) {
                    contentHeader.style.top = '56px';
                }
            }
        }
    });

    // Store cleanup function
    window.cleanupScrollBehavior = () => {
        window.removeEventListener('scroll', handleScroll);
    };

    // Test function for debugging
    window.testScrollBehavior = () => {
        console.log('=== Testing scroll behavior ===');
        console.log('FAB element:', fab);
        console.log('FAB Label element:', fabLabel);
        console.log('Bottom App Bar element:', bottomAppBar);
        console.log('Window width:', window.innerWidth);
        console.log('Is mobile:', window.innerWidth <= 768);

        if (fabLabel) {
            console.log('FAB Label current styles:', {
                opacity: fabLabel.style.opacity,
                transform: fabLabel.style.transform,
                display: fabLabel.style.display
            });
        }

        if (bottomAppBar) {
            console.log('Bottom App Bar current styles:', {
                transform: bottomAppBar.style.transform
            });
        }

        console.log('Testing hide...');
        hideScrollElements();

        // Test show after 3 seconds
        setTimeout(() => {
            console.log('Testing show...');
            showScrollElements();
        }, 3000);
    };

    // Manual hide/show functions for testing
    window.hideElements = hideScrollElements;
    window.showElements = showScrollElements;

    // Show scroll elements when back button is pressed
    // window.addEventListener('popstate', () => {
    //     console.log('Browser back button pressed - showing scroll elements');
    //     showScrollElements();
    // });

    // Show scroll elements when content header back button is clicked
    const contentHeaderBackBtn = document.querySelector('.content-header .material-icons:first-child');
    if (contentHeaderBackBtn) {
        contentHeaderBackBtn.addEventListener('click', () => {
            console.log('Content header back button pressed - showing scroll elements');
            showScrollElements();
        });
    }
}

// Bottom Navigation Functions
function initializeBottomNavigation() {
    bottomNavItems = document.querySelectorAll('.bottom-nav-item');

    bottomNavItems.forEach(item => {
        item.addEventListener('click', (e) => {
            createRipple(e, item, true);
            handleBottomNavigation(item.id);
        });
    });
}

function handleBottomNavigation(navId) {
    // Remove active class from all items
    bottomNavItems.forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to clicked item
    document.getElementById(navId).classList.add('active');

    // Handle navigation based on the selected item
    switch(navId) {
        case 'nav-home':
            console.log('Navigate to Home');
            // TODO: Implement home navigation
            break;
        case 'nav-students':
            console.log('Navigate to Students');
            // Already on students page - no action needed
            break;
        case 'nav-stats':
            console.log('Navigate to Stats');
            // TODO: Implement stats navigation
            break;
        case 'nav-attendance':
            console.log('Navigate to Attendance');
            // TODO: Implement attendance navigation
            break;
    }
}

function openAddStudentModal() {
    const studentFields = [
        {
            name: 'firstName',
            label: 'First Name',
            type: 'text',
            required: true,
            placeholder: 'Enter first name'
        },
        {
            name: 'lastName',
            label: 'Last Name',
            type: 'text',
            required: true,
            placeholder: 'Enter last name'
        },
        {
            name: 'email',
            label: 'Email',
            type: 'email',
            required: true,
            placeholder: 'Enter email address'
        },
        {
            name: 'phone',
            label: 'Phone Number',
            type: 'tel',
            placeholder: 'Enter phone number'
        },
        {
            name: 'grade',
            label: 'Grade',
            type: 'select',
            required: true,
            options: [
                { value: '', label: 'Grade' },
                { value: 'Grade 9', label: 'Grade 9' },
                { value: 'Grade 10', label: 'Grade 10' },
                { value: 'Grade 11', label: 'Grade 11' },
                { value: 'Grade 12', label: 'Grade 12' }
            ]
        },
        {
            name: 'birthDate',
            label: 'Birth Date',
            type: 'date',
            required: true
        },
        {
            name: 'gender',
            label: 'Gender',
            type: 'select',
            required: true,
            options: [
                { value: '', label: 'Gender' },
                { value: 'Male', label: 'Male' },
                { value: 'Female', label: 'Female' },
                { value: 'Other', label: 'Other' }
            ]
        },
        {
            name: 'address',
            label: 'Address',
            type: 'textarea',
            placeholder: 'Enter full address',
            rows: 3
        }
    ];

    openModal({
        title: 'Add New Student',
        fields: studentFields,
        submitText: 'Add Student',
        onSubmit: handleAddStudent
    });
}

function openLargeStudentModal() {
    const studentSections = [
        {
            title: 'Personal Information',
            fields: [
                {
                    name: 'firstName',
                    label: 'First Name',
                    type: 'text',
                    required: true,
                    placeholder: 'Enter first name'
                },
                {
                    name: 'middleName',
                    label: 'Middle Name',
                    type: 'text',
                    placeholder: 'Enter middle name'
                },
                {
                    name: 'lastName',
                    label: 'Last Name',
                    type: 'text',
                    required: true,
                    placeholder: 'Enter last name'
                },
                {
                    name: 'birthDate',
                    label: 'Birth Date',
                    type: 'date',
                    required: true
                },
                {
                    name: 'gender',
                    label: 'Gender',
                    type: 'select',
                    required: true,
                    options: [
                        { value: '', label: 'Gender' },
                        { value: 'Male', label: 'Male' },
                        { value: 'Female', label: 'Female' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                {
                    name: 'nationality',
                    label: 'Nationality',
                    type: 'text',
                    placeholder: 'Enter nationality'
                }
            ]
        },
        {
            title: 'Academic Information',
            fields: [
                {
                    name: 'grade',
                    label: 'Grade',
                    type: 'select',
                    required: true,
                    options: [
                        { value: '', label: 'Grade' },
                        { value: 'Grade 9', label: 'Grade 9' },
                        { value: 'Grade 10', label: 'Grade 10' },
                        { value: 'Grade 11', label: 'Grade 11' },
                        { value: 'Grade 12', label: 'Grade 12' }
                    ]
                },
                {
                    name: 'section',
                    label: 'Section',
                    type: 'select',
                    options: [
                        { value: '', label: 'Section' },
                        { value: 'A', label: 'Section A' },
                        { value: 'B', label: 'Section B' },
                        { value: 'C', label: 'Section C' }
                    ]
                },
                {
                    name: 'previousSchool',
                    label: 'Previous School',
                    type: 'text',
                    placeholder: 'Enter previous school name'
                },
                {
                    name: 'enrollmentDate',
                    label: 'Enrollment Date',
                    type: 'date',
                    required: true
                },
                {
                    name: 'academicYear',
                    label: 'Academic Year',
                    type: 'select',
                    required: true,
                    options: [
                        { value: '', label: 'Academic Year' },
                        { value: '2024-2025', label: '2024-2025' },
                        { value: '2025-2026', label: '2025-2026' }
                    ]
                },
                {
                    name: 'specialNeeds',
                    label: 'Special Needs',
                    type: 'textarea',
                    placeholder: 'Describe any special educational needs',
                    rows: 2
                }
            ]
        },
        {
            title: 'Contact Information',
            fields: [
                {
                    name: 'email',
                    label: 'Email',
                    type: 'email',
                    required: true,
                    placeholder: 'Enter email address'
                },
                {
                    name: 'phone',
                    label: 'Phone Number',
                    type: 'tel',
                    placeholder: 'Enter phone number'
                },
                {
                    name: 'emergencyContact',
                    label: 'Emergency Contact',
                    type: 'tel',
                    placeholder: 'Enter emergency contact number'
                },
                {
                    name: 'parentName',
                    label: 'Parent/Guardian Name',
                    type: 'text',
                    required: true,
                    placeholder: 'Enter parent or guardian name'
                },
                {
                    name: 'parentEmail',
                    label: 'Parent Email',
                    type: 'email',
                    placeholder: 'Enter parent email address'
                },
                {
                    name: 'address',
                    label: 'Address',
                    type: 'textarea',
                    placeholder: 'Enter full address',
                    rows: 3,
                    width: 'full'
                }
            ]
        }
    ];

    openModal({
        title: 'Add New Student - Detailed Form',
        sections: studentSections,
        submitText: 'Add Student',
        onSubmit: handleAddStudent,
        large: true
    });
}

function handleAddStudent(data) {
    console.log('Adding new student:', data);

    // Show loading
    showLoadingOverlay('Adding student...');

    // Simulate API call
    setTimeout(() => {
        // Generate new student ID
        const newId = `ST${new Date().getFullYear()}${String(studentsData.length + 1).padStart(3, '0')}`;

        // Create new student object
        const newStudent = {
            id: newId,
            name: `${data.firstName} ${data.middleName ? data.middleName + ' ' : ''}${data.lastName}`,
            level: data.grade,
            birth: new Date(data.birthDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            birthYear: new Date(data.birthDate).getFullYear(),
            gender: data.gender,
            photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
            date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            dateAdded: new Date().toISOString().split('T')[0],
            email: data.email,
            phone: data.phone,
            address: data.address,
            // Additional fields from large form
            middleName: data.middleName,
            nationality: data.nationality,
            section: data.section,
            previousSchool: data.previousSchool,
            enrollmentDate: data.enrollmentDate,
            academicYear: data.academicYear,
            specialNeeds: data.specialNeeds,
            emergencyContact: data.emergencyContact,
            parentName: data.parentName,
            parentEmail: data.parentEmail
        };

        // Add to students data
        studentsData.unshift(newStudent);

        // Refresh the display
        filterData();

        hideLoadingOverlay();

        // Show success message (you can implement a toast/snackbar here)
        console.log('Student added successfully!');
    }, 1000);
}

// Ripple Effect Function
function createRipple(event, element, isDark = false) {
    const circle = document.createElement('span');
    const diameter = Math.max(element.clientWidth, element.clientHeight);
    const radius = diameter / 2;
    
    const rect = element.getBoundingClientRect();
    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - rect.left - radius}px`;
    circle.style.top = `${event.clientY - rect.top - radius}px`;
    circle.classList.add('ripple');
    
    if (isDark) {
        circle.classList.add('ripple-dark');
    }
    
    const ripple = element.getElementsByClassName('ripple')[0];
    if (ripple) {
        ripple.remove();
    }
    
    element.appendChild(circle);
    
    // Remove ripple after animation
    setTimeout(() => {
        circle.remove();
    }, 600);
}

// Sidebar toggle (works for both mobile and desktop)
const menuBtn = document.getElementById('menu-btn');
const sidebar = document.getElementById('sidebar');
const mainContent = document.querySelector('.main-content');
const contentHeader = document.querySelector('.content-header');

menuBtn.addEventListener('click', (e) => {
    createRipple(e, menuBtn);

    if (window.innerWidth <= 768) {
        // Mobile behavior: toggle open class
        sidebar.classList.toggle('open');
    } else {
        // Desktop behavior: toggle collapsed class
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        contentHeader.classList.toggle('expanded');
    }
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    }
});

// Grades submenu toggle
const gradesMenu = document.getElementById('grades-menu');
const gradesSubmenu = document.getElementById('grades-submenu');

gradesMenu.addEventListener('click', (e) => {
    createRipple(e, gradesMenu, true);
    gradesMenu.classList.toggle('expanded');
    gradesSubmenu.classList.toggle('expanded');
});

// Sidebar item interactions
document.querySelectorAll('.sidebar-item:not(#grades-menu)').forEach(item => {
    item.addEventListener('click', (e) => {
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked item
        item.classList.add('active');
    });
});

// Submenu item interactions
document.querySelectorAll('.submenu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked submenu item
        item.classList.add('active');
    });
});

// Edit button interactions
document.querySelectorAll('.edit-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, btn, true);
        const studentName = btn.closest('.student-item').querySelector('.student-name').textContent;
        console.log('Edit student:', studentName);
        // Here you would typically open an edit modal or navigate to edit page
    });
});

// Bottom Sheet Elements
const bottomSheetOverlay = document.getElementById('bottom-sheet-overlay');
const bottomSheet = document.getElementById('bottom-sheet');
const bottomSheetClose = document.getElementById('bottom-sheet-close');
const previewPhoto = document.getElementById('preview-photo');
const previewName = document.getElementById('preview-name');
const previewDetails = document.getElementById('preview-details');

// Bottom Sheet Functions
function showBottomSheet(studentData) {
    // Populate student preview
    previewPhoto.style.backgroundImage = studentData.photo;
    previewName.textContent = studentData.name;
    previewDetails.innerHTML = `<span class="student-level">${studentData.level}</span>${studentData.details}`;

    // Show bottom sheet
    bottomSheetOverlay.classList.add('active');
    bottomSheet.classList.add('active');

    // Store current student data for actions
    bottomSheet.dataset.studentId = studentData.id;
    bottomSheet.dataset.studentName = studentData.name;
}

function hideBottomSheet() {
    bottomSheetOverlay.classList.remove('active');
    bottomSheet.classList.remove('active');
}

// Student Detail Page Functions
function showStudentDetail(student) {
    const studentDetailOverlay = document.getElementById('student-detail-overlay');

    // Populate student detail data
    populateStudentDetail(student);

    // Show student detail page
    studentDetailOverlay.classList.add('active');

    // Hide app bars and navigation for full screen experience
    const appBar = document.querySelector('.app-bar');
    const bottomAppBar = document.getElementById('bottom-app-bar');

    if (appBar) appBar.style.display = 'none';
    if (bottomAppBar) bottomAppBar.style.display = 'none';
}

function hideStudentDetail() {
    const studentDetailOverlay = document.getElementById('student-detail-overlay');
    studentDetailOverlay.classList.remove('active');

    // Show app bars and navigation again
    const appBar = document.querySelector('.app-bar');
    const bottomAppBar = document.getElementById('bottom-app-bar');

    if (appBar) appBar.style.display = 'flex';
    if (window.innerWidth <= 768 && bottomAppBar) {
        bottomAppBar.style.display = 'flex';
    }
}

function populateStudentDetail(student) {
    // Profile section
    document.getElementById('detail-photo').style.backgroundImage = `url('${student.photo}')`;
    document.getElementById('detail-name').textContent = student.name;
    document.getElementById('detail-name-ar').textContent = student.nameAr;
    document.getElementById('detail-id').textContent = student.id;

    // General information
    document.getElementById('detail-birth').textContent = student.birth;
    document.getElementById('detail-birth-place').textContent = student.birthPlace || 'Not specified';
    document.getElementById('detail-gender').textContent = student.gender;
    document.getElementById('detail-status').textContent = student.status;

    // Academic information
    document.getElementById('detail-grade-fr').textContent = student.levelFr;
    document.getElementById('detail-grade-ar').textContent = student.levelAr;

    // Financial information
    document.getElementById('detail-fees').textContent = `${student.fees} DH`;
    document.getElementById('detail-paid').textContent = `${student.amountPaid} DH`;
    document.getElementById('detail-remaining').textContent = `${student.remaining} DH`;
}

// Results tab functionality
function initializeResultsTabs() {
    const resultsTabs = document.querySelectorAll('.results-tab');
    const resultsTabContents = document.querySelectorAll('.results-tab-content');

    resultsTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            createRipple(e, tab);

            // Remove active class from all tabs and contents
            resultsTabs.forEach(t => t.classList.remove('active'));
            resultsTabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            tab.classList.add('active');

            // Show corresponding content
            const tabType = tab.dataset.tab;
            const targetContent = document.getElementById(`results-${tabType}`);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

// Bottom Sheet Event Listeners
bottomSheetClose.addEventListener('click', (e) => {
    createRipple(e, bottomSheetClose, true);
    hideBottomSheet();
});

bottomSheetOverlay.addEventListener('click', (e) => {
    if (e.target === bottomSheetOverlay) {
        hideBottomSheet();
    }
});

// Student item interactions
document.querySelectorAll('.student-item').forEach(item => {
    item.addEventListener('click', (e) => {
        // Don't trigger if edit button was clicked
        if (e.target.closest('.edit-btn')) return;

        createRipple(e, item, true);

        // Extract student data
        const studentData = {
            id: item.dataset.studentId || item.querySelector('.student-name').textContent.replace(/\s+/g, '').toLowerCase(),
            name: item.querySelector('.student-name').textContent,
            level: item.querySelector('.student-level').textContent,
            details: item.querySelector('.student-details').textContent.replace(item.querySelector('.student-level').textContent, '').trim(),
            photo: item.querySelector('.student-photo').style.backgroundImage
        };

        showBottomSheet(studentData);
    });
});

// Add ripple effects to app bar action buttons
document.querySelectorAll('.app-bar .actions .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon);
    });
});

// Add ripple effects to content header icons
document.querySelectorAll('.content-header .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon, true);
    });
});

// Bottom Sheet Action Handlers
document.getElementById('action-details').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentId = bottomSheet.dataset.studentId;
    const student = studentsData.find(s => s.id === studentId);

    if (student) {
        hideBottomSheet();
        showStudentDetail(student);
    }
});

document.getElementById('action-edit').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Edit student:', studentName);
    hideBottomSheet();
    // Here you would open edit modal or navigate to edit page
});

document.getElementById('action-payment').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Add payment record for:', studentName);
    hideBottomSheet();
    // Here you would open payment modal or navigate to payment page
});

document.getElementById('action-delete').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    const studentId = bottomSheet.dataset.studentId;

    // Hide bottom sheet first
    hideBottomSheet();

    // Show confirmation dialog
    showConfirmDialog({
        title: 'Delete Student',
        message: `Are you sure you want to delete ${studentName}? This action cannot be undone.`,
        icon: 'delete',
        iconType: 'error',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        confirmStyle: 'danger',
        onConfirm: () => {
            console.log('Delete student:', studentName, 'ID:', studentId);
            // Here you would call delete API and remove from DOM
            // Example: removeStudentFromList(studentId);
        },
        onCancel: () => {
            console.log('Delete cancelled for:', studentName);
        }
    });
});

// Add ripple effects to bottom sheet actions
document.querySelectorAll('.bottom-sheet-action').forEach(action => {
    action.addEventListener('click', (e) => {
        if (!e.currentTarget.id) { // Only add ripple if not handled by specific action
            createRipple(e, action, true);
        }
    });
});

// Keyboard support for bottom sheet
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && bottomSheetOverlay.classList.contains('active')) {
        hideBottomSheet();
    }
});

// Responsive sidebar handling
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        // Desktop: remove mobile classes
        sidebar.classList.remove('open');
    } else {
        // Mobile: remove desktop classes and reset to mobile behavior
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');
        contentHeader.classList.remove('expanded');
    }

    // Update QR FAB visibility
    updateQRFabVisibility();
});

// Function to update QR FAB visibility based on screen size
function updateQRFabVisibility() {
    const qrFab = document.getElementById('qr-fab');
    if (qrFab) {
        if (window.innerWidth <= 768) {
            // Show on mobile
            qrFab.style.display = 'flex';
        } else {
            // Hide on desktop
            qrFab.style.display = 'none';
        }
    }
}

// Initialize QR FAB visibility on page load
document.addEventListener('DOMContentLoaded', () => {
    updateQRFabVisibility();
});

// Offcanvas Component
const offcanvasOverlay = document.getElementById('offcanvas-overlay');
const offcanvas = document.getElementById('offcanvas');
const offcanvasClose = document.getElementById('offcanvas-close');
const moreMenuBtn = document.getElementById('more-menu-btn');

// Offcanvas Functions
function showOffcanvas() {
    offcanvasOverlay.classList.add('active');
    offcanvas.classList.add('active');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function hideOffcanvas() {
    offcanvasOverlay.classList.remove('active');
    offcanvas.classList.remove('active');
    document.body.style.overflow = ''; // Restore scrolling
}

// Offcanvas Event Listeners
moreMenuBtn.addEventListener('click', (e) => {
    createRipple(e, moreMenuBtn);
    showOffcanvas();
});

offcanvasClose.addEventListener('click', (e) => {
    createRipple(e, offcanvasClose);
    hideOffcanvas();
});

offcanvasOverlay.addEventListener('click', (e) => {
    if (e.target === offcanvasOverlay) {
        hideOffcanvas();
    }
});

// Offcanvas Menu Item Event Listeners
document.getElementById('menu-profile').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Edit Profile clicked');
    hideOffcanvas();
    // TODO: Implement profile editing functionality
});

document.getElementById('menu-settings').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Settings clicked');
    hideOffcanvas();
    // TODO: Implement settings functionality
});

document.getElementById('menu-notifications').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Notifications clicked');
    hideOffcanvas();
    // TODO: Implement notifications functionality
});

document.getElementById('menu-help').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Help & Support clicked');
    hideOffcanvas();
    // TODO: Implement help functionality
});

document.getElementById('menu-logout').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);

    // Hide offcanvas first
    hideOffcanvas();

    // Show confirmation dialog
    showConfirmDialog({
        title: 'Logout',
        message: 'Are you sure you want to logout?',
        icon: 'logout',
        iconType: 'info',
        confirmText: 'Logout',
        cancelText: 'Cancel',
        confirmStyle: 'primary',
        onConfirm: () => {
            console.log('User logged out');
            // TODO: Implement logout functionality
            // Example: window.location.href = '/login';
        },
        onCancel: () => {
            console.log('Logout cancelled');
        }
    });
});

// Keyboard support for offcanvas
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && offcanvasOverlay.classList.contains('active')) {
        hideOffcanvas();
    }
});

// Add ripple effects to offcanvas menu items
document.querySelectorAll('.offcanvas-menu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        if (!e.currentTarget.id) { // Only add ripple if not handled by specific action
            createRipple(e, item, true);
        }
    });
});

// Filter Offcanvas Component
const filterOffcanvasOverlay = document.getElementById('filter-offcanvas-overlay');
const filterOffcanvas = document.getElementById('filter-offcanvas');
const filterOffcanvasClose = document.getElementById('filter-offcanvas-close');
const filterBtn = document.getElementById('filter-btn');

// Filter Offcanvas Functions
function showFilterOffcanvas() {
    filterOffcanvasOverlay.classList.add('active');
    filterOffcanvas.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function hideFilterOffcanvas() {
    filterOffcanvasOverlay.classList.remove('active');
    filterOffcanvas.classList.remove('active');
    document.body.style.overflow = '';
}

// Initialize Filter Components
function initializeFilterComponents() {
    // Initialize Grade Filter Select
    const gradeFilterEl = document.getElementById('grade-filter-select');
    if (gradeFilterEl && window.mdc && window.mdc.select) {
        filterGradeSelect = new mdc.select.MDCSelect(gradeFilterEl);
        filterGradeSelect.listen('MDCSelect:change', () => {
            gradeFilter = filterGradeSelect.value;
            filterData();
        });
    }

    // Initialize Gender Filter Select
    const genderFilterEl = document.getElementById('gender-filter-select');
    if (genderFilterEl && window.mdc && window.mdc.select) {
        filterGenderSelect = new mdc.select.MDCSelect(genderFilterEl);
        filterGenderSelect.listen('MDCSelect:change', () => {
            genderFilter = filterGenderSelect.value;
            filterData();
        });
    }

    // Initialize Birth Year Filter Select
    const birthYearFilterEl = document.getElementById('birth-year-filter-select');
    if (birthYearFilterEl && window.mdc && window.mdc.select) {
        filterBirthYearSelect = new mdc.select.MDCSelect(birthYearFilterEl);
        filterBirthYearSelect.listen('MDCSelect:change', () => {
            birthYearFilter = filterBirthYearSelect.value;
            filterData();
        });
    }

    // Initialize Date Pickers
    const dateFromInput = document.getElementById('date-from-filter');
    const dateToInput = document.getElementById('date-to-filter');

    if (dateFromInput && window.flatpickr) {
        dateFromPicker = flatpickr(dateFromInput, {
            dateFormat: 'Y-m-d',
            placeholder: 'Select from date',
            onChange: function(selectedDates, dateStr) {
                dateFromFilter = dateStr;
                filterData();
            }
        });
    }

    if (dateToInput && window.flatpickr) {
        dateToPicker = flatpickr(dateToInput, {
            dateFormat: 'Y-m-d',
            placeholder: 'Select to date',
            onChange: function(selectedDates, dateStr) {
                dateToFilter = dateStr;
                filterData();
            }
        });
    }

    // Initialize Date Input Text Fields
    const dateFromTextField = document.querySelector('#date-from-filter').closest('.mdc-text-field');
    const dateToTextField = document.querySelector('#date-to-filter').closest('.mdc-text-field');

    if (dateFromTextField && window.mdc && window.mdc.textField) {
        new mdc.textField.MDCTextField(dateFromTextField);
    }

    if (dateToTextField && window.mdc && window.mdc.textField) {
        new mdc.textField.MDCTextField(dateToTextField);
    }

    // Initialize Clear Filters Button
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    if (clearFiltersBtn && window.mdc && window.mdc.ripple) {
        new mdc.ripple.MDCRipple(clearFiltersBtn);
    }
}

// Clear all filters function
function clearAllFilters() {
    // Reset filter variables
    gradeFilter = '';
    genderFilter = '';
    birthYearFilter = '';
    dateFromFilter = '';
    dateToFilter = '';

    // Reset filter components
    if (filterGradeSelect) {
        filterGradeSelect.selectedIndex = 0;
    }
    if (filterGenderSelect) {
        filterGenderSelect.selectedIndex = 0;
    }
    if (filterBirthYearSelect) {
        filterBirthYearSelect.selectedIndex = 0;
    }
    if (dateFromPicker) {
        dateFromPicker.clear();
    }
    if (dateToPicker) {
        dateToPicker.clear();
    }

    // Apply filters (which will show all data)
    filterData();
}

// Filter Offcanvas Event Listeners
filterBtn.addEventListener('click', (e) => {
    createRipple(e, filterBtn, true);
    showFilterOffcanvas();
});

filterOffcanvasClose.addEventListener('click', (e) => {
    createRipple(e, filterOffcanvasClose);
    hideFilterOffcanvas();
});

filterOffcanvasOverlay.addEventListener('click', (e) => {
    if (e.target === filterOffcanvasOverlay) {
        hideFilterOffcanvas();
    }
});

document.getElementById('clear-filters-btn').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget);
    clearAllFilters();
});

// Initialize student detail page
function initializeStudentDetail() {
    // Back button handler
    const studentDetailBack = document.getElementById('student-detail-back');
    if (studentDetailBack) {
        studentDetailBack.addEventListener('click', (e) => {
            createRipple(e, studentDetailBack);
            hideStudentDetail();
        });
    }

    // More button handler
    const studentDetailMore = document.getElementById('student-detail-more');
    if (studentDetailMore) {
        studentDetailMore.addEventListener('click', (e) => {
            createRipple(e, studentDetailMore);
            // TODO: Implement more options menu
            console.log('More options clicked');
        });
    }

    // Initialize results tabs
    initializeResultsTabs();

    // Close on overlay click
    const studentDetailOverlay = document.getElementById('student-detail-overlay');
    if (studentDetailOverlay) {
        studentDetailOverlay.addEventListener('click', (e) => {
            if (e.target === studentDetailOverlay) {
                hideStudentDetail();
            }
        });
    }

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && studentDetailOverlay && studentDetailOverlay.classList.contains('active')) {
            hideStudentDetail();
        }
    });
}

// Initialize filter components when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add a small delay to ensure all MDC components are initialized
    setTimeout(() => {
        initializeFilterComponents();
        initializeStudentDetail();
    }, 1000);
});

// Keyboard support for filter offcanvas
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && filterOffcanvasOverlay.classList.contains('active')) {
        hideFilterOffcanvas();
    }
});
